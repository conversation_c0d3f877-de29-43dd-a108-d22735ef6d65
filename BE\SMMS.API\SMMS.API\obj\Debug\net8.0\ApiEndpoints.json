[{"ContainingType": "SMMS.API.Controllers.AuthController", "Method": "CreateAccount", "RelativePath": "api/auth/create-account", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "VerifyPassword", "Type": "System.String", "IsRequired": false}, {"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "IdToken", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.AuthController", "Method": "SendOtp", "RelativePath": "api/auth/parent/send-otp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.SendOtpRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.AuthController", "Method": "VerifyOtp", "RelativePath": "api/auth/parent/verify-otp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.VerifyOtpRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.AuthController", "Method": "VerifyPhoneNumber", "RelativePath": "api/auth/verify-phonenumber", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.VerifyPhoneRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "GetAllBlogs", "RelativePath": "api/blogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "CreateBlog", "RelativePath": "api/blogs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "ImageFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ImageUrl", "Type": "System.String", "IsRequired": false}, {"Name": "Content", "Type": "System.String", "IsRequired": false}, {"Name": "Excerpt", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "GetBlogById", "RelativePath": "api/blogs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "UpdateBlog", "RelativePath": "api/blogs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "ImageFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ImageUrl", "Type": "System.String", "IsRequired": false}, {"Name": "Content", "Type": "System.String", "IsRequired": false}, {"Name": "Excerpt", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "DeleteBlog", "RelativePath": "api/blogs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "IncrementView", "RelativePath": "api/blogs/{id}/view", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "GetMyBlogs", "RelativePath": "api/blogs/my-blogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "UploadImage", "RelativePath": "api/blogs/upload-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.BlogController", "Method": "GetBlogsByUserId", "RelativePath": "api/blogs/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetActivityConsentsForHealthActivity", "RelativePath": "api/medical-events/activity-consents/health-activities/{healthActivityId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "healthActivityId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetActivityConsentsForVaccinationCampaign", "RelativePath": "api/medical-events/activity-consents/vaccination-campaigns/{vaccinationCampaignId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "vaccinationCampaignId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "CreateHealthActivity", "RelativePath": "api/medical-events/health-activities", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.HealthActivityRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "UpdateHealthActivity", "RelativePath": "api/medical-events/health-activities/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.HealthActivityRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "DeleteHealthActivity", "RelativePath": "api/medical-events/health-activities/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "UpdateHealthActivityStatus", "RelativePath": "api/medical-events/health-activities/{id}/approve-or-reject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "action", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetAllHealthActivities", "RelativePath": "api/medical-events/health-activities/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetHealthActivitiesWithoutPending", "RelativePath": "api/medical-events/health-activities/approve-reject", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetPendingHealthActivities", "RelativePath": "api/medical-events/health-activities/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "CreateVaccinationCampaign", "RelativePath": "api/medical-events/vaccination-campaigns", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.VaccinationCampaignRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "UpdateVaccinationCampaign", "RelativePath": "api/medical-events/vaccination-campaigns/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.VaccinationCampaignRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "DeleteVaccinationCampaign", "RelativePath": "api/medical-events/vaccination-campaigns/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "ApproveVaccinationCampaign", "RelativePath": "api/medical-events/vaccination-campaigns/{id}/approve-or-reject", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "action", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetAllVaccineCampaign", "RelativePath": "api/medical-events/vaccination-campaigns/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetApprovedVaccinationCampaigns", "RelativePath": "api/medical-events/vaccination-campaigns/approve-reject", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalEventController", "Method": "GetPendingVaccinationCampaigns", "RelativePath": "api/medical-events/vaccination-campaigns/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "CreateMedicalIncident", "RelativePath": "api/medical/incident", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.CreateMedicalIncidentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetAllMedicalIncident", "RelativePath": "api/medical/incident", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "DeleteMedicalIncident", "RelativePath": "api/medical/incident/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetMedicalIncidentById", "RelativePath": "api/medical/incident/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "UpdateMedicalIncident", "RelativePath": "api/medical/incident/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.UpdateMedicalIncidentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "UpdateIncidentStatus", "RelativePath": "api/medical/incident/status/{id}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "CreateMedicalRequest", "RelativePath": "api/medical/request", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.CreateMedicalRequestRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetAllMedicalRequests", "RelativePath": "api/medical/request", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetMedicalRequestById", "RelativePath": "api/medical/request/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "UpdateMedicalRequest", "RelativePath": "api/medical/request/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.UpdateMedicalRequestRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "DeleteMedicalRequest", "RelativePath": "api/medical/request/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "RecordMedicationAdministration", "RelativePath": "api/medical/request/administration", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.RecordMedicationAdministrationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetDailyMedicationSchedule", "RelativePath": "api/medical/request/daily/{date}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.DateTime", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetTodayMedicationSchedule", "RelativePath": "api/medical/request/daily/today", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetMedicalRequestsByParent", "RelativePath": "api/medical/request/parent/{parentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "SearchMedicalRequests", "RelativePath": "api/medical/request/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "medicationName", "Type": "System.String", "IsRequired": false}, {"Name": "studentId", "Type": "System.String", "IsRequired": false}, {"Name": "date", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetMedicalRequestsByStudent", "RelativePath": "api/medical/request/student/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "CreateMedicalStock", "RelativePath": "api/medical/stock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.CreateMedicalStockRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetAllMedicalStock", "RelativePath": "api/medical/stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "DeleteMedicalStock", "RelativePath": "api/medical/stock/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "GetMedicalStockById", "RelativePath": "api/medical/stock/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "UpdateMedicalStock", "RelativePath": "api/medical/stock/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.UpdateMedicalStockRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "CreateMedicalUsage", "RelativePath": "api/medical/usage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.CreateMedicalUsageRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "DeleteMedicalUsage", "RelativePath": "api/medical/usage/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.MedicalController", "Method": "UpdateMedicalUsage", "RelativePath": "api/medical/usage/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.UpdateMedicalUsageRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NotificationController", "Method": "GetMyNotifications", "RelativePath": "api/notifications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NotificationController", "Method": "MarkAsRead", "RelativePath": "api/notifications/{id}/read", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NotificationController", "Method": "DeleteReadNotifications", "RelativePath": "api/notifications/delete-read", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NotificationController", "Method": "MarkAllAsRead", "RelativePath": "api/notifications/mark-all-read", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NotificationController", "Method": "GetReadNotifications", "RelativePath": "api/notifications/read", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NotificationController", "Method": "GetUnreadNotifications", "RelativePath": "api/notifications/unread", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "UpdateConselingStatus", "RelativePath": "api/nurse/conseling-schedules-status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.AcceptConselingScheduleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "GetNurseCheckup", "RelativePath": "api/nurse/get-all-checkup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "GetAllConselingSchedules", "RelativePath": "api/nurse/get-all-conseling-schedules", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "GetAllHealthCheckupRecords", "RelativePath": "api/nurse/health-checkup-records", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "UpdateHealthCheckupRecord", "RelativePath": "api/nurse/health-checkup-records/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.HealthCheckupUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "GetCheckupRecordsByDate", "RelativePath": "api/nurse/health-checkup-records/by-date", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "CreateHealthProfile", "RelativePath": "api/nurse/health-profiles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": false}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.HealthProfileRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "GetHealthProfile", "RelativePath": "api/nurse/health-profiles/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "UpdateHealthProfile", "RelativePath": "api/nurse/health-profiles/{studentId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.HealthProfileRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "DeleteHealthProfile", "RelativePath": "api/nurse/health-profiles/{studentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "ImportHealthProfiles", "RelativePath": "api/nurse/health-profiles/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "GetAllVaccinationRecords", "RelativePath": "api/nurse/vaccination-records", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "UpdateVaccinationRecord", "RelativePath": "api/nurse/vaccination-records/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "req", "Type": "SMMS.Application.DataObject.RequestObject.VaccinationRecordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.NurseController", "Method": "GetVaccineRecordsByDate", "RelativePath": "api/nurse/vaccination-records/by-date", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "date", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "UpdateActivityConsentStatus", "RelativePath": "api/parents/activity-consents/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "GetActivityConsentsByActivityId", "RelativePath": "api/parents/activity-consents/HealthActivity-or-VaccinationCampaign", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityId", "Type": "System.String", "IsRequired": false}, {"Name": "activityType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "GetActivityConsentsForMyChildren", "RelativePath": "api/parents/activity-consents/my-children", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "RequestConselingSchedule", "RelativePath": "api/parents/conseling-schedules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.ConselingRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "GetAllConselingSchedules", "RelativePath": "api/parents/get-all-conseling-schedules", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "GetHealthCheckup", "RelativePath": "api/parents/get-all-student-health-checkup", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "GetVaccinationRecordsForMyChildren", "RelativePath": "api/parents/get-all-vaccination-records", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "GetMyStudents", "RelativePath": "api/parents/students", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.ParentController", "Method": "UpdateStudentHealthProfile", "RelativePath": "api/parents/students/{studentId}/health-profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.HealthProfileRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.RoleController", "Method": "GetAllRoles", "RelativePath": "api/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.RoleController", "Method": "CreateRole", "RelativePath": "api/roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.RoleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.RoleController", "Method": "GetRoleById", "RelativePath": "api/roles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.RoleController", "Method": "UpdateRole", "RelativePath": "api/roles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.RoleRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.RoleController", "Method": "DeleteRole", "RelativePath": "api/roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.SchoolClassController", "Method": "GetAllSchoolClasses", "RelativePath": "api/school-classes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.SchoolClassController", "Method": "CreateSchoolClass", "RelativePath": "api/school-classes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.SchoolClassRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.SchoolClassController", "Method": "GetSchoolClassById", "RelativePath": "api/school-classes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.SchoolClassController", "Method": "UpdateSchoolClass", "RelativePath": "api/school-classes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.SchoolClassRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.SchoolClassController", "Method": "DeleteSchoolClass", "RelativePath": "api/school-classes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetAllUsers", "RelativePath": "api/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.UserCreateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetUserById", "RelativePath": "api/users/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SMMS.Application.DataObject.RequestObject.UserUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "api/users/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "ImportStudents", "RelativePath": "api/users/import-students", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetStudentsByParentId", "RelativePath": "api/users/parents/{parentId}/students", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetAllParents", "RelativePath": "api/users/parents/get-all-parent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetMyProfile", "RelativePath": "api/users/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "UpdateMyProfile", "RelativePath": "api/users/profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetAllStudents", "RelativePath": "api/users/students", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "CreateStudent", "RelativePath": "api/users/students", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Gender", "Type": "System.String", "IsRequired": false}, {"Name": "DateOfBirth", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ClassId", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "parentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetStudentsById", "RelativePath": "api/users/students/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "UpdateStudent", "RelativePath": "api/users/students/{studentId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}, {"Name": "FullName", "Type": "System.String", "IsRequired": false}, {"Name": "Gender", "Type": "System.String", "IsRequired": false}, {"Name": "DateOfBirth", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ClassId", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "DeleteStudent", "RelativePath": "api/users/students/{studentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.UserController", "Method": "GetStudentByStudentCode", "RelativePath": "api/users/students/code/{studentCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SMMS.API.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SMMS.API.WeatherForecast, SMMS.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]