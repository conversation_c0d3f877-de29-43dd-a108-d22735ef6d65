{"openapi": "3.0.1", "info": {"title": "My API", "version": "v1"}, "paths": {"/api/auth/parent/send-otp": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendOtpRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendOtpRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendOtpRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/parent/verify-otp": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/verify-phonenumber": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/create-account": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "Email", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "PhoneNumber", "in": "query", "required": true, "schema": {"maxLength": 10, "minLength": 0, "pattern": "^\\d+$", "type": "string"}}, {"name": "Password", "in": "query", "required": true, "schema": {"maxLength": 100, "minLength": 6, "type": "string", "format": "password"}}, {"name": "VerifyPassword", "in": "query", "required": true, "schema": {"type": "string", "format": "password"}}, {"name": "FullName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "IdToken", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Image": {"type": "string", "format": "binary"}}}, "encoding": {"Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/blogs": {"get": {"tags": ["Blog"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Blog"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Title": {"type": "string"}, "ImageFile": {"type": "string", "format": "binary"}, "ImageUrl": {"type": "string"}, "Content": {"type": "string"}, "Excerpt": {"type": "string"}}}, "encoding": {"Title": {"style": "form"}, "ImageFile": {"style": "form"}, "ImageUrl": {"style": "form"}, "Content": {"style": "form"}, "Excerpt": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/blogs/{id}": {"get": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Title": {"type": "string"}, "ImageFile": {"type": "string", "format": "binary"}, "ImageUrl": {"type": "string"}, "Content": {"type": "string"}, "Excerpt": {"type": "string"}}}, "encoding": {"Title": {"style": "form"}, "ImageFile": {"style": "form"}, "ImageUrl": {"style": "form"}, "Content": {"style": "form"}, "Excerpt": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/blogs/{id}/view": {"post": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/blogs/user/{userId}": {"get": {"tags": ["Blog"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/blogs/my-blogs": {"get": {"tags": ["Blog"], "responses": {"200": {"description": "OK"}}}}, "/api/blogs/upload-image": {"post": {"tags": ["Blog"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"image": {"type": "string", "format": "binary"}}}, "encoding": {"image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical/stock": {"post": {"tags": ["Medical"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalStockRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalStockRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMedicalStockRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Medical"], "responses": {"200": {"description": "OK"}}}}, "/api/medical/stock/{id}": {"delete": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalStockRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalStockRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalStockRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical/incident": {"post": {"tags": ["Medical"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalIncidentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalIncidentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMedicalIncidentRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Medical"], "parameters": [{"name": "studentId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical/incident/{id}": {"delete": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalIncidentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalIncidentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalIncidentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical/incident/status/{id}": {"patch": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/MedicalIncidentStatus"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical/usage": {"post": {"tags": ["Medical"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalUsageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalUsageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMedicalUsageRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical/usage/{id}": {"delete": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalUsageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalUsageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalUsageRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical/request": {"post": {"tags": ["Medical"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalRequestRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMedicalRequestRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMedicalRequestRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Medical"], "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/{id}": {"get": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalRequestRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalRequestRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMedicalRequestRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Medical"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/student/{studentId}": {"get": {"tags": ["Medical"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/parent/{parentId}": {"get": {"tags": ["Medical"], "parameters": [{"name": "parentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/daily/{date}": {"get": {"tags": ["Medical"], "parameters": [{"name": "date", "in": "path", "required": true, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/daily/today": {"get": {"tags": ["Medical"], "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/administration": {"post": {"tags": ["Medical"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecordMedicationAdministrationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RecordMedicationAdministrationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RecordMedicationAdministrationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/administration/pending": {"get": {"tags": ["Medical"], "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical/request/search": {"get": {"tags": ["Medical"], "parameters": [{"name": "medicationName", "in": "query", "schema": {"type": "string"}}, {"name": "studentId", "in": "query", "schema": {"type": "string"}}, {"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/health-activities/all": {"get": {"tags": ["MedicalEvent"], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/health-activities/approve-reject": {"get": {"tags": ["MedicalEvent"], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/health-activities/pending": {"get": {"tags": ["MedicalEvent"], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/health-activities": {"post": {"tags": ["MedicalEvent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthActivityRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthActivityRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthActivityRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/health-activities/{id}/approve-or-reject": {"put": {"tags": ["MedicalEvent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "action", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/health-activities/{id}": {"put": {"tags": ["MedicalEvent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthActivityRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthActivityRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthActivityRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["MedicalEvent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/vaccination-campaigns/all": {"get": {"tags": ["MedicalEvent"], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/vaccination-campaigns/pending": {"get": {"tags": ["MedicalEvent"], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/vaccination-campaigns/approve-reject": {"get": {"tags": ["MedicalEvent"], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/vaccination-campaigns": {"post": {"tags": ["MedicalEvent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VaccinationCampaignRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VaccinationCampaignRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VaccinationCampaignRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/vaccination-campaigns/{id}/approve-or-reject": {"put": {"tags": ["MedicalEvent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "action", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/vaccination-campaigns/{id}": {"put": {"tags": ["MedicalEvent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VaccinationCampaignRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VaccinationCampaignRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VaccinationCampaignRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["MedicalEvent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/activity-consents/health-activities/{healthActivityId}": {"get": {"tags": ["MedicalEvent"], "parameters": [{"name": "healthActivityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/medical-events/activity-consents/vaccination-campaigns/{vaccinationCampaignId}": {"get": {"tags": ["MedicalEvent"], "parameters": [{"name": "vaccinationCampaignId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/notifications": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "OK"}}}}, "/api/notifications/unread": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "OK"}}}}, "/api/notifications/read": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "OK"}}}}, "/api/notifications/{id}/read": {"put": {"tags": ["Notification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/notifications/mark-all-read": {"put": {"tags": ["Notification"], "responses": {"200": {"description": "OK"}}}}, "/api/notifications/delete-read": {"delete": {"tags": ["Notification"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/health-profiles/{studentId}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/health-profiles": {"post": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/health-profiles/import": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/get-all-checkup": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/health-checkup-records": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/health-checkup-records/by-date": {"get": {"tags": ["Nurse"], "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/health-checkup-records/{id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthCheckupUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthCheckupUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthCheckupUpdateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/get-all-conseling-schedules": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/conseling-schedules-status": {"put": {"tags": ["Nurse"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcceptConselingScheduleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AcceptConselingScheduleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AcceptConselingScheduleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccination-records": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccination-records/{id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VaccinationRecordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VaccinationRecordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VaccinationRecordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccination-records/by-date": {"get": {"tags": ["Nurse"], "parameters": [{"name": "date", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/parents/students": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parents/get-all-student-health-checkup": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parents/get-all-vaccination-records": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parents/get-all-conseling-schedules": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parents/conseling-schedules": {"post": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConselingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConselingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConselingRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parents/students/{studentId}/health-profile": {"put": {"tags": ["Parent"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthProfileRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parents/activity-consents/my-children": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parents/activity-consents/{id}/status": {"put": {"tags": ["Parent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatus"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatus"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApprovalStatus"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parents/activity-consents/HealthActivity-or-VaccinationCampaign": {"get": {"tags": ["Parent"], "parameters": [{"name": "activityId", "in": "query", "schema": {"type": "string"}}, {"name": "activityType", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles": {"get": {"tags": ["Role"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Role"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/roles/{id}": {"get": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RoleRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/school-classes": {"get": {"tags": ["SchoolClass"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["SchoolClass"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchoolClassRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchoolClassRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SchoolClassRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/school-classes/{id}": {"get": {"tags": ["SchoolClass"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["SchoolClass"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchoolClassRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchoolClassRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SchoolClassRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["SchoolClass"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/profile": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["User"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"FullName": {"type": "string"}, "Phone": {"type": "string"}, "Image": {"type": "string", "format": "binary"}}}, "encoding": {"FullName": {"style": "form"}, "Phone": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/import-students": {"post": {"tags": ["User"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/students": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["User"], "parameters": [{"name": "parentId", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"FullName": {"type": "string"}, "Gender": {"type": "string"}, "DateOfBirth": {"type": "string", "format": "date-time"}, "ClassId": {"type": "string"}, "Image": {"type": "string", "format": "binary"}}}, "encoding": {"FullName": {"style": "form"}, "Gender": {"style": "form"}, "DateOfBirth": {"style": "form"}, "ClassId": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/students/{studentId}": {"get": {"tags": ["User"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["User"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"FullName": {"type": "string"}, "Gender": {"type": "string"}, "DateOfBirth": {"type": "string", "format": "date-time"}, "ClassId": {"type": "string"}, "Image": {"type": "string", "format": "binary"}}}, "encoding": {"FullName": {"style": "form"}, "Gender": {"style": "form"}, "DateOfBirth": {"style": "form"}, "ClassId": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["User"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/students/code/{studentCode}": {"get": {"tags": ["User"], "parameters": [{"name": "studentCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/parents/get-all-parent": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/users/parents/{parentId}/students": {"get": {"tags": ["User"], "parameters": [{"name": "parentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"AcceptConselingScheduleRequest": {"type": "object", "properties": {"conselingScheduleId": {"type": "string", "nullable": true}, "scheduledTime": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/components/schemas/ApprovalStatus"}}, "additionalProperties": false}, "ApprovalStatus": {"enum": ["Pending", "Approved", "Rejected"], "type": "string"}, "ConselingRequest": {"type": "object", "properties": {"studentId": {"type": "string", "nullable": true}, "healthCheckupId": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "requestedDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CreateMedicalIncidentRequest": {"type": "object", "properties": {"studentId": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "incidentDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CreateMedicalRequestRequest": {"required": ["medicalRequestItems", "parentId", "studentId"], "type": "object", "properties": {"studentId": {"minLength": 1, "type": "string"}, "parentId": {"minLength": 1, "type": "string"}, "medicalRequestItems": {"type": "array", "items": {"$ref": "#/components/schemas/MedicalRequestItem"}}}, "additionalProperties": false}, "CreateMedicalStockRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "expiryDate": {"type": "string", "format": "date-time"}, "detailInformation": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateMedicalUsageRequest": {"type": "object", "properties": {"medicalIncidentId": {"type": "string", "nullable": true}, "medicalUsageDetails": {"type": "array", "items": {"$ref": "#/components/schemas/MedicalUsageDetail"}, "nullable": true}}, "additionalProperties": false}, "HealthActivityRequest": {"required": ["classIds"], "type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "scheduledDate": {"type": "string", "format": "date-time"}, "classIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "HealthCheckupUpdateRequest": {"type": "object", "properties": {"vision": {"type": "string", "nullable": true}, "hearing": {"type": "string", "nullable": true}, "dental": {"type": "string", "nullable": true}, "bmi": {"type": "number", "format": "double"}, "abnormalNote": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HealthProfileRequest": {"type": "object", "properties": {"vision": {"type": "string", "nullable": true}, "hearing": {"type": "string", "nullable": true}, "dental": {"type": "string", "nullable": true}, "bmi": {"type": "number", "format": "double"}, "abnormalNote": {"type": "string", "nullable": true}, "vaccinationHistory": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MedicalIncidentStatus": {"enum": ["Pending", "Processed", "Unprocessed"], "type": "string"}, "MedicalRequestItem": {"required": ["dosage", "endDate", "form", "frequency", "medicationName", "route", "startDate", "timeToAdminister", "totalQuantity"], "type": "object", "properties": {"medicationName": {"minLength": 1, "type": "string"}, "form": {"minLength": 1, "type": "string"}, "dosage": {"minLength": 1, "type": "string"}, "route": {"minLength": 1, "type": "string"}, "frequency": {"type": "integer", "format": "int32"}, "totalQuantity": {"type": "integer", "format": "int32"}, "timeToAdminister": {"type": "array", "items": {"type": "string"}}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MedicalStockStatus": {"enum": ["Available", "OutOfStock"], "type": "string"}, "MedicalUsageDetail": {"type": "object", "properties": {"medicalStockId": {"type": "string", "nullable": true}, "dosage": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RecordMedicationAdministrationRequest": {"required": ["doseGiven", "medicalRequestId", "wasTaken"], "type": "object", "properties": {"administrationId": {"type": "string", "nullable": true}, "medicalRequestId": {"minLength": 1, "type": "string"}, "doseGiven": {"minLength": 1, "type": "string"}, "wasTaken": {"type": "boolean"}, "administeredAt": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoleRequest": {"type": "object", "properties": {"roleName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SchoolClassRequest": {"type": "object", "properties": {"className": {"type": "string", "nullable": true}, "classRoom": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SendOtpRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateMedicalIncidentRequest": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/MedicalIncidentStatus"}, "incidentDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UpdateMedicalRequestRequest": {"required": ["dosage", "endDate", "form", "frequency", "medicationName", "route", "startDate", "timeToAdminister", "totalQuantity"], "type": "object", "properties": {"medicationName": {"minLength": 1, "type": "string"}, "form": {"minLength": 1, "type": "string"}, "dosage": {"minLength": 1, "type": "string"}, "route": {"minLength": 1, "type": "string"}, "frequency": {"type": "integer", "format": "int32"}, "totalQuantity": {"type": "integer", "format": "int32"}, "timeToAdminister": {"type": "array", "items": {"type": "string"}}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateMedicalStockRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "expiryDate": {"type": "string", "format": "date-time"}, "detailInformation": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/MedicalStockStatus"}}, "additionalProperties": false}, "UpdateMedicalUsageRequest": {"type": "object", "properties": {"medicalStockId": {"type": "string", "nullable": true}, "dosage": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserCreateRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "roleId": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserUpdateRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VaccinationCampaignRequest": {"required": ["classIds"], "type": "object", "properties": {"name": {"type": "string", "nullable": true}, "vaccineName": {"type": "string", "nullable": true}, "exp": {"type": "string", "format": "date-time"}, "mfg": {"type": "string", "format": "date-time"}, "vaccineType": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "classIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "VaccinationRecordRequest": {"type": "object", "properties": {"resultNote": {"type": "string", "nullable": true}, "vaccinatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VerifyOtpRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerifyPhoneRequest": {"type": "object", "properties": {"idToken": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Chỉ nhập JWT token, không cần 'Bearer' prefix", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}