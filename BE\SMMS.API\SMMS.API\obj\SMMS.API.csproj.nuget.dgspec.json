{"format": 1, "restore": {"D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.API\\SMMS.API.csproj": {}}, "projects": {"D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.API\\SMMS.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.API\\SMMS.API.csproj", "projectName": "SMMS.API", "projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.API\\SMMS.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj": {"projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj"}, "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj"}, "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj": {"projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FirebaseAdmin": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj", "projectName": "SMMS.Application", "projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Application\\SMMS.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj"}, "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj": {"projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.105.0, )"}, "CloudinaryDotNet": {"target": "Package", "version": "[1.27.5, )"}, "EPPlus": {"target": "Package", "version": "[8.0.5, )"}, "FirebaseAdmin": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj", "projectName": "SMMS.Domain", "projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj", "projectName": "SMMS.Infrastructure", "projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\SMMS.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"projectPath": "D:\\CODE\\C#\\SWP391\\SWP391_SSMS\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}