namespace SMMS.Application.DataObject.ResponseObject
{
    public class PendingMedicationAdministrationResponse
    {
        public string Id { get; set; }
        public string MedicalRequestId { get; set; }
        public string StudentId { get; set; }
        public string StudentName { get; set; }
        public string StudentClass { get; set; }
        public string MedicationName { get; set; }
        public string Form { get; set; }
        public string Route { get; set; }
        public DateTime ScheduledAt { get; set; }
        public string PlannedDosage { get; set; } // Từ MedicalRequest
        public string? Notes { get; set; }
        public bool IsOverdue { get; set; }
        public TimeSpan? OverdueBy { get; set; }
    }
}
