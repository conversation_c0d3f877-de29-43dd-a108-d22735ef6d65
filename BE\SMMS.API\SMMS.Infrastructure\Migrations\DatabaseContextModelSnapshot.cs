﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SMMS.Infrastructure.Context;

#nullable disable

namespace SMMS.Infrastructure.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    partial class DatabaseContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("SMMS.Domain.Entity.ActivityConsent", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ActivityType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("HealthActivityId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("ScheduleTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccinationCampaignId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("HealthActivityId");

                    b.HasIndex("StudentId");

                    b.HasIndex("UserId");

                    b.HasIndex("VaccinationCampaignId");

                    b.ToTable("ActivityConsent");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Blog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Excerpt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("View")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Blog");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.ConselingSchedule", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("HealthCheckupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicalStaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("MeetingDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("HealthCheckupId");

                    b.HasIndex("MedicalStaffId");

                    b.HasIndex("ParentId");

                    b.HasIndex("StudentId");

                    b.ToTable("ConselingSchedule");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Document", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Document");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("HealthActivity");

                    b.HasData(
                        new
                        {
                            Id = "4054029f-6c19-48ad-a69a-9db389b38dba",
                            CreatedBy = "a601ff8f-4687-44dd-b92d-fac75d9fafbd",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(5371), new TimeSpan(0, 0, 0, 0, 0)),
                            Description = "Yearly health check for students",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Annual Health Check",
                            ScheduledDate = new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Status = 0,
                            UserId = "a601ff8f-4687-44dd-b92d-fac75d9fafbd"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivityClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("HealthActivityId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("SchoolClassId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("HealthActivityId");

                    b.HasIndex("SchoolClassId");

                    b.ToTable("HealthActivityClasses");

                    b.HasData(
                        new
                        {
                            Id = "2e8cff02-b4a8-4b0a-8a5e-146935740657",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(5453), new TimeSpan(0, 0, 0, 0, 0)),
                            HealthActivityId = "4054029f-6c19-48ad-a69a-9db389b38dba",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            SchoolClassId = "92ac6d8f-4f4d-4af3-b080-6e953a043087"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthCheckupRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AbnormalNote")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("BMI")
                        .HasColumnType("float");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dental")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HealthActivityId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Hearing")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsLatest")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("RecordDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.Property<string>("Vision")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("HealthActivityId");

                    b.HasIndex("StudentId");

                    b.ToTable("HealthCheckupRecord");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AbnormalNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("BMI")
                        .HasColumnType("float");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dental")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Hearing")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccinationHistory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Vision")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("StudentId");

                    b.ToTable("HealthProfile");

                    b.HasData(
                        new
                        {
                            Id = "b076b0c9-7280-4482-8a8f-6f9d787e550b",
                            AbnormalNote = "None",
                            BMI = 20.5,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(5273), new TimeSpan(0, 0, 0, 0, 0)),
                            Dental = "No cavities",
                            Hearing = "Normal",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            StudentId = "9e73591c-db8c-46af-8350-ef2f5d422e4e",
                            VaccinationHistory = "Fully vaccinated",
                            Vision = "20/20"
                        },
                        new
                        {
                            Id = "7540a5f6-2e82-4412-a96e-30b1a2bc3458",
                            AbnormalNote = "Monitor dental health",
                            BMI = 19.800000000000001,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(5282), new TimeSpan(0, 0, 0, 0, 0)),
                            Dental = "Minor cavities",
                            Hearing = "Normal",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            StudentId = "b6a5fe21-49d7-4f78-a47d-dd52e6c284ab",
                            VaccinationHistory = "Fully vaccinated",
                            Vision = "20/25"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalIncident", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("IncidentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("StudentId");

                    b.HasIndex("UserId");

                    b.ToTable("MedicalIncident");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalRequest", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dosage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Form")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Frequency")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicationName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ParentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RemainingQuantity")
                        .HasColumnType("int");

                    b.Property<string>("Route")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TimeToAdminister")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("StudentId");

                    b.HasIndex("UserId");

                    b.ToTable("MedicalRequest");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalStock", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DetailInformation")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("MedicalStock");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalUsage", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dosage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicalIncidentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("MedicalName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MedicalStockId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MedicalIncidentId");

                    b.HasIndex("MedicalStockId");

                    b.ToTable("MedicalUsage");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicationRequestAdministration", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AdministeredAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("AdministeredBy")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DoseGiven")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicalRequestId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("WasTaken")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("AdministeredBy");

                    b.HasIndex("MedicalRequestId");

                    b.ToTable("MedicationRequestAdministration");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Notification", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EventId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Notification");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Otp", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("ExpirationTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("OtpCode")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Otps");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Role", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Role");

                    b.HasData(
                        new
                        {
                            Id = "bf5171eb-3632-4cac-be4a-4650c640464e",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 509, DateTimeKind.Unspecified).AddTicks(4459), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Admin"
                        },
                        new
                        {
                            Id = "3f31540a-cb1e-440c-b615-3cef260be756",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 509, DateTimeKind.Unspecified).AddTicks(4469), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 509, DateTimeKind.Unspecified).AddTicks(4469), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Manager"
                        },
                        new
                        {
                            Id = "9a6109c1-fd78-46a2-a2b5-df3a064318a7",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 509, DateTimeKind.Unspecified).AddTicks(4501), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 509, DateTimeKind.Unspecified).AddTicks(4501), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Nurse"
                        },
                        new
                        {
                            Id = "95c31307-9482-4a78-8f32-e25660e317ec",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 509, DateTimeKind.Unspecified).AddTicks(4506), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 509, DateTimeKind.Unspecified).AddTicks(4506), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Parent"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.SchoolClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ClassName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClassRoom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("SchoolClass");

                    b.HasData(
                        new
                        {
                            Id = "92ac6d8f-4f4d-4af3-b080-6e953a043087",
                            ClassName = "Class 10A",
                            ClassRoom = "Room 101",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(4990), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Quantity = 30
                        },
                        new
                        {
                            Id = "2f77e28f-7a51-4248-ad78-e00a6da64913",
                            ClassName = "Class 10B",
                            ClassRoom = "Room 102",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(4993), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Quantity = 28
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Student", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ClassId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ParentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("StudentCode")
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("nvarchar(max)")
                        .HasComputedColumnSql("'STD' + CAST([StudentNumber] AS VARCHAR(10))");

                    b.Property<int>("StudentNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StudentNumber"));

                    b.HasKey("Id");

                    b.HasIndex("ClassId");

                    b.HasIndex("ParentId");

                    b.ToTable("Student");

                    b.HasData(
                        new
                        {
                            Id = "9e73591c-db8c-46af-8350-ef2f5d422e4e",
                            ClassId = "92ac6d8f-4f4d-4af3-b080-6e953a043087",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(5084), new TimeSpan(0, 0, 0, 0, 0)),
                            DateOfBirth = new DateTime(2010, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FullName = "Nguyen Van A",
                            Gender = "Male",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            ParentId = "7b10c4fa-ab91-4abc-91e7-d80d2649d6cf",
                            StudentNumber = 0
                        },
                        new
                        {
                            Id = "b6a5fe21-49d7-4f78-a47d-dd52e6c284ab",
                            ClassId = "2f77e28f-7a51-4248-ad78-e00a6da64913",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(5091), new TimeSpan(0, 0, 0, 0, 0)),
                            DateOfBirth = new DateTime(2010, 8, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FullName = "Tran Thi B",
                            Gender = "Female",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            ParentId = "7b10c4fa-ab91-4abc-91e7-d80d2649d6cf",
                            StudentNumber = 0
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = "0c96e8ca-e342-4f9a-8d44-2050d11c249e",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 622, DateTimeKind.Unspecified).AddTicks(6053), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "KICM vippro",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$K91Ds8TYWeO.SfZRsbeiWOvjbgyJiBcPBJcA3deXpFWl711.U2Anm",
                            Phone = "0987654321",
                            RoleId = "bf5171eb-3632-4cac-be4a-4650c640464e"
                        },
                        new
                        {
                            Id = "a601ff8f-4687-44dd-b92d-fac75d9fafbd",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 740, DateTimeKind.Unspecified).AddTicks(175), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "Jack97",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$phz6HWHt1OIhQvIJayFteub8NAR8fLw1WkCvs2i1Q94gIoikBLGpK",
                            Phone = "0912345678",
                            RoleId = "9a6109c1-fd78-46a2-a2b5-df3a064318a7"
                        },
                        new
                        {
                            Id = "4927825c-6639-4265-aa1d-ae993d83a974",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 858, DateTimeKind.Unspecified).AddTicks(3897), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "FireFly",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$tBph2C6EHLoo2kOQTbOhW.G7qe4eRmLSpAxn0VD.Nx8Kw3J9hDYY2",
                            Phone = "0987651234",
                            RoleId = "3f31540a-cb1e-440c-b615-3cef260be756"
                        },
                        new
                        {
                            Id = "7b10c4fa-ab91-4abc-91e7-d80d2649d6cf",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(3894), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "KietBap",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$nyqF24C2w8ah5f2k5UDq8.hw5Q5N.7b69iSQ1rK.f1t8qkxAEWJ4C",
                            Phone = "0987051234",
                            RoleId = "95c31307-9482-4a78-8f32-e25660e317ec"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaign", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("EXP")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("MFG")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccineName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VaccineType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("VaccinationCampaign");

                    b.HasData(
                        new
                        {
                            Id = "c93688cd-9235-4450-9a55-75dd6497c245",
                            CreatedBy = "a601ff8f-4687-44dd-b92d-fac75d9fafbd",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(5577), new TimeSpan(0, 0, 0, 0, 0)),
                            EXP = new DateTime(2025, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            MFG = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Flu Vaccination",
                            StartDate = new DateTime(2024, 11, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Status = 0,
                            UserId = "a601ff8f-4687-44dd-b92d-fac75d9fafbd",
                            VaccineName = "Flu Vaccine",
                            VaccineType = "Flu"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaignClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("SchoolClassId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccinationCampaignId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("SchoolClassId");

                    b.HasIndex("VaccinationCampaignId");

                    b.ToTable("VaccinationCampaignClasses");

                    b.HasData(
                        new
                        {
                            Id = "aa37725a-5e79-4a13-aba7-62f077b2175e",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 14, 8, 37, 33, 978, DateTimeKind.Unspecified).AddTicks(8390), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            SchoolClassId = "2f77e28f-7a51-4248-ad78-e00a6da64913",
                            VaccinationCampaignId = "c93688cd-9235-4450-9a55-75dd6497c245"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ResultNote")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("VaccinatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("VaccinationCampaignId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("StudentId");

                    b.HasIndex("VaccinationCampaignId");

                    b.ToTable("VaccinationRecord");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.ActivityConsent", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthActivity", "HealthActivity")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("HealthActivityId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.VaccinationCampaign", "VaccinationCampaign")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("VaccinationCampaignId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("HealthActivity");

                    b.Navigation("Student");

                    b.Navigation("User");

                    b.Navigation("VaccinationCampaign");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Blog", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("Blogs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.ConselingSchedule", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthCheckupRecord", "HealthCheckupRecord")
                        .WithMany("ConselingSchedules")
                        .HasForeignKey("HealthCheckupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "MedicalStaff")
                        .WithMany("StaffConselingSchedules")
                        .HasForeignKey("MedicalStaffId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "Parent")
                        .WithMany("ParentConselingSchedules")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("ConselingSchedules")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("HealthCheckupRecord");

                    b.Navigation("MedicalStaff");

                    b.Navigation("Parent");

                    b.Navigation("Student");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivity", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("HealthActivities")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivityClass", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthActivity", "HealthActivity")
                        .WithMany("HealthActivityClasses")
                        .HasForeignKey("HealthActivityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.SchoolClass", "SchoolClass")
                        .WithMany()
                        .HasForeignKey("SchoolClassId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("HealthActivity");

                    b.Navigation("SchoolClass");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthCheckupRecord", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthActivity", "HealthActivity")
                        .WithMany("HealthCheckupRecords")
                        .HasForeignKey("HealthActivityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("HealthCheckupRecords")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("HealthActivity");

                    b.Navigation("Student");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthProfile", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("HealthProfiles")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Student");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalIncident", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("MedicalIncidents")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("MedicalIncidents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Student");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalRequest", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "Parent")
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("MedicalRequests")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("MedicalRequests")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("Student");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalUsage", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.MedicalIncident", "MedicalIncident")
                        .WithMany("MedicalUsages")
                        .HasForeignKey("MedicalIncidentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.MedicalStock", "MedicalStock")
                        .WithMany("MedicalUsages")
                        .HasForeignKey("MedicalStockId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("MedicalIncident");

                    b.Navigation("MedicalStock");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicationRequestAdministration", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "Administrator")
                        .WithMany()
                        .HasForeignKey("AdministeredBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SMMS.Domain.Entity.MedicalRequest", "MedicalRequest")
                        .WithMany("MedicationRequestAdministrations")
                        .HasForeignKey("MedicalRequestId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Administrator");

                    b.Navigation("MedicalRequest");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Notification", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("Notifications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Otp", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("Otps")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Student", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.SchoolClass", "SchoolClass")
                        .WithMany("Students")
                        .HasForeignKey("ClassId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "Parent")
                        .WithMany("Students")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("SchoolClass");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.User", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaign", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaignClass", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.SchoolClass", "SchoolClass")
                        .WithMany()
                        .HasForeignKey("SchoolClassId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.VaccinationCampaign", "VaccinationCampaign")
                        .WithMany("VaccinationCampaignClasses")
                        .HasForeignKey("VaccinationCampaignId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("SchoolClass");

                    b.Navigation("VaccinationCampaign");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationRecord", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("VaccinationRecords")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.VaccinationCampaign", "VaccinationCampaign")
                        .WithMany("VaccinationRecords")
                        .HasForeignKey("VaccinationCampaignId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Student");

                    b.Navigation("VaccinationCampaign");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivity", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("HealthActivityClasses");

                    b.Navigation("HealthCheckupRecords");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthCheckupRecord", b =>
                {
                    b.Navigation("ConselingSchedules");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalIncident", b =>
                {
                    b.Navigation("MedicalUsages");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalRequest", b =>
                {
                    b.Navigation("MedicationRequestAdministrations");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalStock", b =>
                {
                    b.Navigation("MedicalUsages");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Role", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.SchoolClass", b =>
                {
                    b.Navigation("Students");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Student", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("ConselingSchedules");

                    b.Navigation("HealthCheckupRecords");

                    b.Navigation("HealthProfiles");

                    b.Navigation("MedicalIncidents");

                    b.Navigation("MedicalRequests");

                    b.Navigation("VaccinationRecords");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.User", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("Blogs");

                    b.Navigation("HealthActivities");

                    b.Navigation("MedicalIncidents");

                    b.Navigation("MedicalRequests");

                    b.Navigation("Notifications");

                    b.Navigation("Otps");

                    b.Navigation("ParentConselingSchedules");

                    b.Navigation("StaffConselingSchedules");

                    b.Navigation("Students");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaign", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("VaccinationCampaignClasses");

                    b.Navigation("VaccinationRecords");
                });
#pragma warning restore 612, 618
        }
    }
}
